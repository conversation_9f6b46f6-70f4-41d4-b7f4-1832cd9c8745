<!DOCTYPE HTML>
<html>

<!-- Mirrored from amrita.olabs.edu.in/olab/html5/?sub=PHY&cat=CLA&exp=Simple-Pendulum&tempId=olab&linktoken=c98e1e4daaf8e515d5918800dcc25074&elink_lan=mr-IN&elink_title=%e0%a4%b8%e0%a4%be%e0%a4%a7%e0%a4%be%20%e0%a4%b2%e0%a5%8b%e0%a4%b2%e0%a4%95 by HTTrack Website Copier/3.x [XR&CO'2014], Sat, 04 Nov 2023 16:37:02 GMT -->
<!-- AmritaCREATE 2023 --><meta http-equiv="content-type" content="text/html;charset=UTF-8" /><!-- /Added by AmritaCREATE 2023 -->
<head>
<!-- Enable IE9 Standards mode -->
<meta http-equiv="X-UA-Compatible" content="IE=9" >
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title></title>
<link rel="stylesheet" type="text/css" media="all" href="../../common/css/reset.css" />
<link rel="stylesheet" type="text/css" media="all" href="../../common/css/grid.css" />
<link href="../../common/css/mainstyle.css" rel="stylesheet" type="text/css" />
<script src="../../common/js/jquery-latest.js"></script>
<!--[if IE]><script src="../../common/js/excanvas.js"></script><![endif]-->
<link  type="text/css" rel="stylesheet" href="css/simstyle.css" />
<link  href="../../common/js/trip/trip.css" rel="stylesheet" type="text/css" />
<script language="javascript" type="text/javascript" src="../../common/js/jquery.i18n.js"></script>
<script type="text/javascript" src="js/simcontrols.js"></script>
<script type="text/javascript" language="javascript" src="../../common/js/jquery/ui/1.10.2/jquery-ui-1.10.2.custom.min.js"></script>
<script language="javascript" type="text/javascript" src="../../common/js/trip/trip.min.js"></script>
<script type="text/javascript" language="javascript" src="../../common/js/touchconvert.js"></script>
<script type="text/javascript" src="js/jquery-css-transform.js"></script>
<script language="javascript"  >
var language_script= 'mr-IN';
var language=language_script.toString();
	language=language.slice(0, 2);
	gt.gettext.setLocale(language);
</script>
<script type='text/javascript'  src='locale/mr-IN/language.json'></script>





<!--[if gte IE 9]>
  <style type="text/css">
    .gradient {
       filter: none;
    }
  </style>
<![endif]-->
<script src="js/js-webshim/1.9.7/minified/extras/modernizr-custom.js"></script>
<script> 
if( !Modernizr.inputtypes.range ){  
		document.write("<script type=\"text/javascript\" src=\"js/js-webshim/1.9.7/minified/polyfiller.js\"></"+"script>");
        $(document).ready(function(){
			$.webshims.setOptions("waitReady", false);
     		$.webshims.polyfill('forms-ext');
		});  
    };  
// Set up the yepnope (Modernizr.load) directives... 
/*Modernizr.load([
{
	
    // Test if Input Range is supported using Modernizr
    test: Modernizr.inputtypes.range,
	    // If ranges are not supported, load the slider script and CSS file
	
    nope: 
	
	[     
        // The slider CSS file
        'js/js-webshim/minified/extras/mousepress.js'       
        // The slider JS file
        ,'js/js-webshim/minified/polyfiller.js'     
    ],
    complete : function () {
	$.webshims.setOptions("waitReady", false);
      $.webshims.polyfill('forms-ext');
    }
}
]);*/

</script>

</head>
<body>
<div class="main">
<header id="silumatorTemp">
  <div class="g99 logo">
  </div>
  <!-- end .g99 -->
    <div class="g495 mainTitle">
    <p id="expName"></p>
  </div>
  <!-- end .g495 -->
    <div class="g198 menuSet">
          
  </div>
  <!-- end .g198 -->
<div class="g792 bannerFoot">
<ul id="olabmenuBar">
  	<li><a href="#">SAVE</a></li>
    <li><a href="#">FULLSCREEN</a></li>
    <li><a href="#">EXIT</a></li>
  </ul>
</div>
    <!-- end .grid_8 -->
</header><!-- /header -->
<div class="g594 canvasHolder"> 
    <div id="canvasBox">
<script type="text/javascript" language="javascript">
	var simPath="index.html";
</script>
<img id="background" src="images/back.png"/>
<img id="stand" src="images/stand.png"/>

<div id="thread" style="position:absolute; width:3px; height:100px; left: 394px; top: 180px; z-index:100;">
    <div id="thread1" ></div>
    <img class="bob" src="images/domeSteel.png" />
</div>

<div id="dropArea"></div>

<div  id="watchCont" >
	<img id="stopwatch" src="images/Clock.png" />
	<div id="timerstyle"><b>min&nbsp;&nbsp; sec &nbsp; ms</b></div>
	<div id="timer">00:00:000</div>
	<div class="start" id ='start' onClick="start()"> START</div>
	<div class="start" id="stop" onClick="stop()">STOP</div>
</div>

<div id="ProtracorDiv" class="rotate">
    <div id="protractorInside" >
        <img id="protractorImg" src="images/protractor.png"/>
        <div id="arrowDivPro1" onclick="rotateanticlockPro()">
        	<img id="protractorarrowImg1" src="images/protracorarrow1.png"/>
		</div>

		<div id="arrowDivPro2" onclick="rotateClockwisePro()">
			<img id="protractorarrowImg2" src="images/protracorarrow2.png"/>
		</div>
	</div>
</div></div>
</div>
<div class="g198 controlHolder">
<ul>
	<li><h1 id="solnCntrl">Solution Controls<span></span></h1>
		<div class="varBox">
          	<p class="varTitle" id="selectEnv"></p>
  			<select class="dropBox" name="envmnt" id="envmnt" onchange="comboEnvi(this.value)">
			</select>
       
            <p class="varTitle" id="selectShape"></p>
  			<select class="dropBox" name="shape" id="shape" onChange="comboShape(this.value)">
			</select>
          
            <p class="varTitle" id="selectMatrial"></p>
  			<select class="dropBox" name="material" id="material" onChange="comboMaterial(this.value)">
			</select>
           
            <p class="varTitle" id="selectWire">Select Wire</p>
  			<select class="dropBox" name="wire" id="wire" onChange="comboWire(this.value)" >
			</select>
           
			<p class="varTitle"><span id="pendlm">Pendulum Length(m):</span>
            	<input type="text" id="input1" style="width:30px;"  onkeydown="return onlyNumbers(this)">
                <input type="range" class="rangeSlider" min="0.5" max="1.5" id="DR" name="DR" value="1" step="0.1" />
			</p>
  
  			<div class="rangeVals">
  				<span class="minrange">0.5</span><span class="maxrange">1.5</span></div>
  				<div class="clear"></div>	
	 			<p class="varTitle"><span id="changeDiamtr">Change Diameter(mm):</span>
                	<input type="text" id="input2" style="width:30px;"  onkeydown="return onlyNumbers(this)">
      				<input type="range" class="rangeSlider" min="10" max="25" id="DR1" name="DR" value="15" step="1"  />
               	</p>
  
			<div class="rangeVals">
  				<span class="minrange">10</span><span class="maxrange">25</span>
  				<div class="clear">
            </div>
  			<p class="varTitle" id="ans_lbl" style="display:none;"></p>	<br/>	
			<p align="center" style="margin-top: -11px;" >
                <input type="button" class="subButton protractor" id="protractorBtn" onclick="ShowProtractor();" value="Show Protractor"/>
            </p>
       	 	<p align="center">
                <input type="button" class="subButton" id="answer" value="Answer" onclick="answer_ftn()" disabled="disabled"/>
            </p>
      			<input type="hidden" id="hdplay" value="play">
        	<p align="center">
                <input type="button" class="subButton" id="play" name="play" value="Play/Pause"  disabled="disabled"/>
            </p>	
        	<p align="center">
                <input type="button" class="subButton" id="reset" name="reset" value="Reset" disabled="disabled"  />
            </p>
	</li>
</ul>
</div>
<script type="text/javascript">
 var expTitle="Simple Pendulum";
 document.getElementById("expName").innerHTML=expTitle;
</script>   <footer id="tempFooter">
 <div class="g396 footer">
<p class="labName">Developed by Amrita University Under research grant from
<br>Ministry of Electronics and Information Technology</p>  
</div>
  <!-- end .g396 -->
  <div class="g396 footer">
    <p class="copyName"></p>
  </div>
    <div class="clear"></div>
    </footer> <!-- /footer -->
 
</div>
</body>

<!-- Mirrored from amrita.olabs.edu.in/olab/html5/?sub=PHY&cat=CLA&exp=Simple-Pendulum&tempId=olab&linktoken=c98e1e4daaf8e515d5918800dcc25074&elink_lan=mr-IN&elink_title=%e0%a4%b8%e0%a4%be%e0%a4%a7%e0%a4%be%20%e0%a4%b2%e0%a5%8b%e0%a4%b2%e0%a4%95 by HTTrack Website Copier/3.x [XR&CO'2014], Sat, 04 Nov 2023 16:37:03 GMT -->
</html>
