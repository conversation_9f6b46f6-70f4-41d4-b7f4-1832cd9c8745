<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OLabs Physics Simulators - 9th & 10th Grade</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .simulator-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .simulator-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            background-color: #fafafa;
            transition: transform 0.2s;
        }
        .simulator-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .simulator-card h3 {
            color: #34495e;
            margin-top: 0;
        }
        .simulator-card p {
            color: #666;
            margin-bottom: 15px;
        }
        .simulator-link {
            display: inline-block;
            background-color: #3498db;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            transition: background-color 0.2s;
        }
        .simulator-link:hover {
            background-color: #2980b9;
        }
        .description {
            text-align: center;
            margin-bottom: 20px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔬 OLabs Physics Simulators</h1>
        <p class="description">Interactive HTML5 Physics Simulators for 9th & 10th Grade Students</p>
        
        <div class="simulator-grid">
            <div class="simulator-card">
                <h3>🌉 Meter Bridge (Parallel Resistance)</h3>
                <p>Study equivalent resistance of resistors connected in parallel using a meter bridge setup.</p>
                <a href="simulators/MeterBridge_Parallel/index.html" class="simulator-link" target="_blank">Launch Simulator</a>
            </div>
            
            <div class="simulator-card">
                <h3>🎯 Simple Pendulum</h3>
                <p>Investigate the motion of a simple pendulum and measure its time period with different parameters.</p>
                <a href="simulators/Simple_Pendulum/index.html" class="simulator-link" target="_blank">Launch Simulator</a>
            </div>
            
            <div class="simulator-card">
                <h3>⚡ Ohm's Law</h3>
                <p>Verify Ohm's law by studying the relationship between voltage, current, and resistance.</p>
                <a href="simulators/Ohms_Law/index.html" class="simulator-link" target="_blank">Launch Simulator</a>
            </div>
            
            <div class="simulator-card">
                <h3>🔍 Convex Lens - Image Formation</h3>
                <p>Study image formation by a convex lens and understand magnification concepts.</p>
                <a href="simulators/Convex_Lens_Image_Formation/index.html" class="simulator-link" target="_blank">Launch Simulator</a>
            </div>
            
            <div class="simulator-card">
                <h3>📏 Vernier Calipers</h3>
                <p>Learn to use vernier calipers for precise measurements of length, diameter, and depth.</p>
                <a href="simulators/Vernier_Calipers/index.html" class="simulator-link" target="_blank">Launch Simulator</a>
            </div>
        </div>
        
        <div style="margin-top: 40px; text-align: center; color: #888; font-size: 14px;">
            <p>All simulators are fully functional HTML5 applications with interactive controls and measurements.</p>
            <p>No plugins required - works in any modern web browser.</p>
        </div>
    </div>
</body>
</html>
