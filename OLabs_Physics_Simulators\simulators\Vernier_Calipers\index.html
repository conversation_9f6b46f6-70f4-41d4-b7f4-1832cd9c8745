<!DOCTYPE HTML>
<html>

<!-- Mirrored from amrita.olabs.edu.in/olab/html5/?sub=PHY&cat=CLA&exp=VernierCalipers&tempId=olab_ot&linktoken=caf5b11fa5cb341b0df287e11429037a&elink_lan=tl-IN&elink_title=Vernier%20Calipers by HTTrack Website Copier/3.x [XR&CO'2014], Sat, 04 Nov 2023 16:36:57 GMT -->
<!-- AmritaCREATE 2023 --><meta http-equiv="content-type" content="text/html;charset=UTF-8" /><!-- /Added by AmritaCREATE 2023 -->
<head>
<!-- Enable IE9 Standards mode -->
<meta http-equiv="X-UA-Compatible" content="IE=9" >
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title></title>
<link rel="stylesheet" type="text/css" media="all" href="../../common/css/font.css" />
<link rel="stylesheet" type="text/css" media="all" href="../../common/css/reset.css" />
<link rel="stylesheet" type="text/css" media="all" href="../../common/css/grid.css" />
<link href="../../common/css/mainstyle.css" rel="stylesheet" type="text/css" />
<link href="../../common/js/nanoscroller/nanoscroller.css" rel="stylesheet" type="text/css" />
<script src="../../common/js/nanoscroller/overthrow.min.js"></script>
<script src="../../common/js/jquery/1.7/jquery-1.7.1.js"></script>
<script src="../../common/js/nanoscroller/jquery.nanoscroller.js"></script>
<script type="text/javascript" src="../../common/js/nanoscroller/touchready.js"></script>
<script type="text/javascript" src="../../common/js/nanoscroller/main.js"></script>
<!--[if IE]><script src="../../common/js/excanvas.js"></script><![endif]-->
<link  href="css/simstyle.css" rel="stylesheet" type="text/css" />
<script language="javascript" type="text/javascript" src="../../common/js/jquery.i18n.js"></script>
<script language="javascript" type="text/javascript" src="../../common/js/jquery.i18n.js"></script>
<script language="javascript" type="text/javascript" src="../../common/js/jquery/ui/1.10.2/jquery-ui-1.10.2.custom.min.js"></script>
<script language="javascript" type="text/javascript" src="js/simcontrols.js"></script>
<script type="text/javascript" language="javascript" src="../../common/js/touchconvert.js"></script>

<script language="javascript"  >
var language_script= 'en-IN';
var language=language_script.toString();
	language=language.slice(0, 2);
	gt.gettext.setLocale(language);
</script>
<script type='text/javascript'  src='locale/en-IN/language.json'></script><!--[if gte IE 9]>
  <style type="text/css">
    .gradient {
       filter: none;
    }
  </style>
<![endif]-->
<script src="../../common/js/js-webshim/1.9.7/minified/extras/modernizr-custom.js"></script>
<script> 
if( !Modernizr.inputtypes.range ){  
		document.write("<script type=\"text/javascript\" src=\"../../common/js/js-webshim/1.9.7/minified/polyfiller.js\"></"+"script>");
        $(document).ready(function(){
			$.webshims.setOptions("waitReady", false);
     		$.webshims.polyfill('forms-ext');
		});  
    };  


</script>

</head>
<body>
<div class="main">
<header id="silumatorTemp">
  <div class="g99 logo">
  </div>
  <!-- end .g99 -->
    <div class="g495 mainTitle">
    <p id="expName"></p>
  </div>
  <!-- end .g495 -->
    <div class="g198 menuSet">
          
  </div>
  <!-- end .g198 -->
<div class="g792 bannerFoot">
<ul id="olabmenuBar">
  	<li><a href="#">SAVE</a></li>
    <li><a id="olabFullscrBtn" onClick="parent.simFullScreenOlab()" style="cursor:pointer;" >FULLSCREEN</a></li>
    <li><a href="#">EXIT</a></li>
  </ul>
</div>
    <!-- end .grid_8 -->
</header><!-- /header -->
<div class="g594 canvasHolder"> 
    <div id="canvasBox">
<div id="ExpContainer">
<div id="Lc_labl"></div>
<div id="Vernier_Main">
<div id="Main_Scale">
<div id="Drag_area">

<div id="Verier_Scale"></div>
</div>
<div id="sclDragger"></div>
</div>

<div id="Scale_Needl"></div>
</div>
<div id="Vernier_Zoom">
<div id="Vernier_Zoom_area">
<div id="Big_Still_Scale"></div>
<div id="Zoom_Scale"></div>
</div>
</div>
<div id="Object_Store"></div>
</div></div>
</div>
<div class="g198 controlHolder">
<div class="nano has-scrollbar">
<ul style="right: -17px;" tabindex="0" class="overthrow content description">
    <li>	
	<div class="varBox"> 
    <p class="varTitle" id="CnrtlTitle"></p>
     <ul class="tiles">
<li id="sphere">
<img src="images/sphere.png">
</li>
<p class="varTitle" id="SphereText"></p>
<li id="IronBlock">
<img src="images/IronBlock.png">
</li>
<p class="varTitle" id="IronBlockText"></p>
<li id="Beaker">
<img src="images/Beaker.png">
</li>
<p class="varTitle" id="BeakerText"></p>
<li id="Cylinder">
<img src="images/Cylinder.png">
</li>

<p class="varTitle" id="CylinderText"></p>
</ul>
<div id="Select_Measure">
<p id="Select_Measure_text" class="varTitle"></p><br />
<input type="radio" id="Radio_One"  name="val"/><span class="varTitleTwo" id="FirstRDBttn"></span><br /><br />
<input type="radio" id="Radio_Two" name="val"/><span class="varTitleTwo" id="SecondRDBttn"></span><br /><br />
<input type="radio" id="Radio_Three" name="val"/><span class="varTitleTwo" id="ThirdRDBttn"></span>
</div>
<div id="Reading">
<div id="Rsl_text_div" ></div>
<div class="varTitle" ><span id="ReseltText"></span>
<input type="text" id="TextBox"  />
<div id="Result_Icon_correct"></div>
<div id="Result_Icon_wrong"></div></div>
<input type="button" id="AnswrChkr"    class="subButton"/>
</div>
<input type="button"  id="ResetBtn" class="subButton"/>
</div>
</li>
</ul></div>
</div>
<script type="text/javascript">
 var expTitle="Vernier Calipers";
 document.getElementById("expName").style.size="11px";
 document.getElementById("expName").innerHTML=expTitle;
 
</script>   <footer id="tempFooter">
 <div class="g396 footer">
<p class="labName">Developed by Amrita University Under research grant from
<br>Ministry of Electronics and Information Technology</p>  
</div>
  <!-- end .g396 -->
  <div class="g396 footer">
    <p class="copyName"></p>
  </div>
    <div class="clear"></div>
    </footer> <!-- /footer -->
</div>
</body>

<!-- Mirrored from amrita.olabs.edu.in/olab/html5/?sub=PHY&cat=CLA&exp=VernierCalipers&tempId=olab_ot&linktoken=caf5b11fa5cb341b0df287e11429037a&elink_lan=tl-IN&elink_title=Vernier%20Calipers by HTTrack Website Copier/3.x [XR&CO'2014], Sat, 04 Nov 2023 16:36:57 GMT -->
</html>
<script>
function simFullScreenOlab(){
		var $ = function(selector,context){return(context||document).querySelector(selector)};
        var iframe = $("iframe"),
            domPrefixes = 'Webkit Moz O ms Khtml'.split(' ');

            var fullscreen = function(elem){
            var prefix;
			
            // Mozilla and webkit intialise fullscreen slightly differently
            for ( var i = -1, len = domPrefixes.length; ++i < len; ) 
			{
              prefix = domPrefixes[i].toLowerCase();
              if ( elem[prefix + 'EnterFullScreen'] ) {
                // Webkit uses EnterFullScreen for video
                return prefix + 'EnterFullScreen';
                break;
              } else if( elem[prefix + 'RequestFullScreen'] ) {
                // Mozilla uses RequestFullScreen for all elements and webkit uses it for non video elements
                return prefix + 'RequestFullScreen';
                break;
              }
            }
            return false;
        }; 
        // Webkit uses "requestFullScreen" for non video elements
        var fullscreenother = fullscreen(document.createElement("iframe"));
        if(!fullscreen) {
            alert("Fullscreen won't work, please make sure you're using a browser that supports it and you have enabled the feature");
            return;
        }
		iframe[fullscreenother]();
		(this,this.document);
	    }

</script>
