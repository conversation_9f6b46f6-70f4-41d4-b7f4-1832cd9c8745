$.i18n.hi = {};
 jQuery.i18n.hi.strings ={
	"Ohm's Law": "ओम का नियम",
	"Readings": "रीडिंग",
	"Voltage (V) :": "वोल्ट (V):",
	"Ammeter reading (I) :": "एमीटर की रीडिंग (I ):",
	"Rheostat resistance :": "रिओस्टेट प्रतिरोध:",
	"Length of the resistance wire :": "प्रतिरोध वाले तार की लंबाई:",
	"Show Circuit Diagram": "सर्किट डायग्राम दिखाएं",
	"Results": "परिणाम",
	"Enter the resistance of the wire:": "तार का प्रतिरोध दर्ज करें:",
	"Enter the resistance per cm of the wire:": "तार का प्रति सेमी प्रतिरोध दर्ज करें:",
	"Please enter the value": "मान दर्ज करें",
	" V": " V",
	" A": " A",
	" Show result": "परिणाम दिखाओ",
	"Drag and connect the terminal</br>of each apparatus as shown</br>in the circuit diagram": "खींचें और जैसे सर्किट डायग्राम में दिखाया गया है, प्रत्येक उपकरण का टर्मिनल कनेक्ट करें",
	"Drag the resistance wire</br>to the voltmeter": "प्रतिरोध वाला तार वोल्टमीटर की ओर खींचें",
	"Drag the switch key</br>to the switch": "स्विच वाली कुंजी स्विच की ओर खींचें",
	"Drag the rheostat key to</br>adjust the rheostat resistance": "रिओस्टेट का प्रतिरोध एडजस्ट करने के लिए रिओस्टेट कुंजी खींचें",
	"Select the length of</br>the resistance wire": "प्रतिरोध वाले तार की लंबाई का चयन करें",
	"HELP": "HELP",
	"Reset": "रीसेट करें",
	"Check": "चेक करें",
	"0.5m": "0.5m",
	"0 V": "0 V",
	"0 A": "0 A"
}