#bg{
	 width: 100%;
}
#tableScale{
	position: absolute;
    left: 11%;
    top: 87%;
}
#lensDiv{
	position: absolute;
    left: 24%;
    top: 41%;
}
.main p{
	overflow: visible;
}
#DropboxTest{
	margin-top: 11px;
}
#screenDiv{
	position: absolute;
    left: 360px;
    top: 30%;
}
#screenPopup{
	position: absolute;
    left: 47%;
    top: -25%;
}
#candleDiv{
	position: absolute;
    left: 0px;
    top: 47%;
}
#candlePopup{
	
}
#screenImg{
	position: absolute;
    left: 64%;
    top: 42%;
    filter: blur(0px);
    opacity: 0.7;
	display:none;
	height: 33px;
}
.varBox{
	margin-top: 7px;
}
#candlepopupText{
	position: absolute;
    width: 36px;
    top: 122px;
    height: 22px;
    left: 33px;
    color: white;
}

#screenPopupText{
	position: absolute;
    width: 39px;
    top: 19px;
    height: 22px;
    left: 3px;
	color: white;
}
#lensPopup{
	position: absolute;
    left: 55%;
    top: 102%;
}
#lenspopupText{
	position: absolute;
    width: 39px;
    top: 176px;
    height: 22px;
    left: 56px;
	color: white;
}
#textHeight{
	position: absolute;
	left: 19px;
	top: 21px;
	font-size: 17px;
	color: white;
}
#objectF1{
    left: 98px;
}
#object2F1{
	left: 54px;
}
#objectF2{
	left: 58px;
}
#object2F2{
	left: 50px;
}
.textF{
	top: 82px;
	position: absolute;
	font-size: 21px;
    color: white;
}
#candlePopupDiv{
	position: absolute;
    left: 29px;
    top: 33px;
}
.popUp{
	position: absolute;
	width: 42px;
    top: 104px;
    left: 26px;
}
#screenPopupDiv{
	position: absolute;
    top: 192px;
    left: 76px;
}
#objectF1Div{
	position: absolute;
	top: 191px;
    left: 78px;
}
#objectF1Popup{
	position: absolute;
    top: 104px;
    left: 66px;
}
#objectF1popupText{
	position: absolute;
    left: 72px;
    top: 123px;
    width: 31px;
    color: white;
}
#object2F1Div{
	position: absolute;
	top: 191px;
    left: 78px;
}
#object2F1popupText{
	position: absolute;
    left: 33px;
    top: 124px;
    width: 31px;
    color: white;
}
#objectF2Div{
	position: absolute;
	top: 191px;
    left: 198px;
}
#objectF2popupText{
	position: absolute;
    left: 33px;
    top: 124px;
    width: 31px;
    color: white;
}
#object2F2Div{
	position: absolute;
	top: 191px;
    left: 239px;
}
#object2F2popupText{
	position: absolute;
    left: 33px;
    top: 124px;
    width: 31px;
    color: white;	
}
#resultMagnificationDiv{
	white-space: nowrap;
	display:none;
}
#resultMagnificationDiv p{
	display:inline-block;
}
#resultHeightDiv{
	white-space: nowrap;
	display:none;
}
#resultHeightDiv p{
	display:inline-block;
}
#rightArrowDiv{
	position: absolute;
    left: 87%;
    top: 59%;
	display:none;
}
.arrowText{
	color: white;	
	font-size: 14px;
}
#leftArrowDiv{
	position: absolute;
    left: 4%;
    top: 19%;
	display:none;
}
#Arrow{
	transform: rotate(180deg);
}
.candleflameCls{
	position: absolute;
	display: none;
}
#candle1{
	position: absolute;
}
#candle{
	position: absolute;
}

#objectLensDiv {
    white-space: nowrap;
}

#objectLensDiv p {
    display: inline-block;
}
#lensScreenDiv {
    white-space: nowrap;
}

#lensScreenDiv p {
    display: inline-block;
}
#inference{
    position: absolute;
    left: 86%;
    top: 10%;
    cursor:pointer;
    display: none;
}

/******************************** Quiz style ******************************  */

.questionLayout {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: #0d102eed;
    top: 0%;
    z-index: 1000;
}
.questionNumber{
    position: absolute;
    width: 100%;
    height: 12%;
    background-color: #c0c1c900;
    top: 2%;
    border-bottom: solid #ffffff75;
}
.questionNumberTestDesign{
    position: absolute;
    color: #fbfbfb8f;
    font-size: 1rem;
    top: 20%;
    left: 3%;
    font-family: 'Inconsolata';
}
.questionNumberScoreDesign{
    position: absolute;
    color: #fbfbfb8f;
    font-size: 1rem;
    top: 20%;
    left: 82%;
    font-family: 'Inconsolata';
}
.questionText{
    position: absolute;
    color: #cac9cbb3;
    top: 18%;
    left: 3%;
    width: 94%;
    height: 21%;
}
.questionTextAlignment{
    position: absolute;
    width: 94%;
    height: 75%;
    top: 11%;
    left: 2%;
    font-size: .9rem;
    color: #fcf8f8;
    text-align: justify;
    line-height: 1.3;
    cursor: not-allowed;
  }
.OptionOne{
    position: absolute;
    border: solid 2px #35aad3c7;
    top: 34%;
    width: 88%;
    height: 11%;
    left: 5%;
    border-radius: 10px;
    cursor: pointer;
  }
.OptionTwo{
    position: absolute;
    border: solid 2px #35aad3c7;
    top: 48%;
    width: 88%;
    height: 11%;
    left: 5%;
    border-radius: 10px;
    cursor: pointer;
  }
.OptionThree{
    position: absolute;
    border: solid 2px #35aad3c7;
    top: 62%;
    width: 88%;
    height: 11%;
    left: 5%;
    border-radius: 10px;
    cursor: pointer;
}
.OptionFour{
    position: absolute;
    border: solid 2px #35aad3c7;
    top: 76%;
    width: 88%;
    height: 11%;
    left: 5%;
    border-radius: 10px;
    cursor: pointer;
}
.optionTestDesign {
    position: absolute;
    color: #ffffff;
    font-size: .8rem;
    top: 20%;
    left: 5%;
}
.optionNo
{
  padding-right: 7px;
}
  