@charset "utf-8";
/* CSS Document */

#sphere{
	/*background-image:url(../images/sphere.png);*/
	width:45px;
	height:42px;
	margin-top: 9px;
	margin-left: 24px;
	cursor:pointer;
}
#SphereText{
	margin-left: 16px;
	margin-top: 1px;
}
#IronBlock{
/*	background-image: url(../images/IronBlock.png);*/
	width: 54px;
	height: 39px;
	margin-top: -57px;
	margin-left: 117px;
	cursor: pointer;
}
#IronBlockText{
	margin-left: 107px;
	margin-top: -1px;
}
#Beaker{
/*	background-image: url(../images/Beaker.png);*/
	width: 41px;
	height: 48px;
	margin-left: 28px;
	margin-top: 15px;
	cursor: pointer;
}
#BeakerText{
	margin-left: 16px;
	margin-top: 4px;
}
#Cylinder{
/*	background-image: url(../images/Cylinder.png);*/
	width: 31px;
	height: 52px;
	margin-top: -73px;
	margin-left: 129px;
	cursor: pointer;
}
#CylinderText{
	margin-top: 4px;
	margin-left: 109px;
}

#Select_Measure{
	width: 195px;
	height: 57px;
	margin-top: -161px;
	margin-left: 1;
	color: #626262;
	visibility: hidden;
}
#Radio_One{
	margin-left: 26px;
}
#Radio_Two{
	margin-left: 26px;	
}
#Radio_Three{
	margin-left: 26px;	
}
#Reading{
	width: 195px;
	height: 31px;
	margin-top:0px;
/*	border:#006 1px solid;*/
}
#TextBox{
	width:35px;
}	
#ResetBtn{
	margin-top: 16px;
	visibility: visible;
	margin-left: 37px;
}
#Result_Icon_correct{
	width: 31px;
	height: 45px;
	margin-top:-31px;
	margin-left:150px;
	background-image: url(../images/Correct_Icon.png);
	background-repeat: no-repeat;
}	
#Result_Icon_wrong{
	width: 31px;
	height: 45px;
	margin-top: -37px;
	margin-left: 157px;
	background-image: url(../images/Worng_icon.png);
	background-repeat: no-repeat;
	visibility: hidden;
}
#Rsl_text_div{
	top: -4px;
	font-family: Arial, Helvetica, sans-serif;
	font-size: 12px;
	font-weight: bold;
	color: #666;
	margin-left:11px;
	text-decoration: underline;
}
#FirstRDBttn{
	left: 39px;
	top: 27px;
}
#SecondRDBttn{
	left:39px;
}
#ThirdRDBttn{
	left:39px;
}


/*=============================================*/
#ExpContainer{
	position:absolute;
	width: 560px;
	height: 335px;

}
#Vernier_Main{
	position: absolute;
	width: 560px;
	height: 150px;
	margin-top: 155px;
	
}
#Vernier_Zoom{
	position: absolute;
	width: 238px;
	height: 125px;
	margin-left: 318px;
	margin-top: 5px;
	overflow: hidden;
}
#Main_Scale{
	position: absolute;
	background-image: url(../images/Scale.png);
	width: 544px;
	height: 180px;
	margin-left: 6px;
	margin-top: 9px;
	background-repeat: no-repeat;
	z-index: 2;
}
#Drag_area{
	position: absolute;
	width: 481px;
	height: 150px;
	top: 0px;
	left: 35px;


}
#sclDragger{
	position: absolute;
	width: 481px;
	height: 150px;
	top: 0px;
	left: 35px;
/*	border:#03C 1px solid;*/
	}
#Verier_Scale{
	position: absolute;
	background-image: url(../images/Vernier_Scale.png);
	width: 93px;
	height: 145px;
	top: 3px;
	left: 0px;
	cursor: pointer;
	background-repeat: no-repeat;
}
#Scale_Needl{
	position: absolute;
	background-image: url(../images/Scale_needl.png);
	width: 516px;
	height: 53px;
	left: 9px;
	top: 9px;
	
}
#Vernier_Zoom_area{
	position:absolute;
	background-image:url(../images/Zoom_area.png);
	width:238px;
	height:125px;
	background-repeat:repeat;
	
}
#Big_Still_Scale{
	position: absolute;
	background-image: url(../images/Scale_Big_Still.png);
	width: 341px;
	height: 69px;
	margin-top: 102px;
	margin-left: -44px;
	z-index:4;
	background-repeat:no-repeat;
}
#Zoom_Scale{
	position: absolute;
	background-image: url(../images/Zoom_Scale.png);
	width:1877px;
	height:171px;
	top: -62px;
	left: -174px;
	background-repeat: no-repeat;
}
#Object_Store{
	position: absolute;
	background-image: url(../images/sphere.png);
	width: 57px;
	height: 55px;
	top: 257px;
	left: 43px;
	background-repeat: no-repeat;
	background-position: -1;
	/*border:#009 1px solid;*/
}


.varTitleTwo{
	font-family: Arial, Helvetica, sans-serif;
	font-size: 12px;
	font-weight: 500;
	color: #626262;
	padding-top: 5px;
	text-align: left !important;
}
#Lc_labl {
position: absolute;
top: 5px;
left: 5px;
font-family: Arial, Helvetica, sans-serif;
color: #626262;
font-size: 1.2em;
font-weight: bold;
}
#AnswrChkr{
	margin-left:38px;
	margin-top:-13px;
	}