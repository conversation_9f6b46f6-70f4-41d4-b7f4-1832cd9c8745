.trip-content {
	color: #DDDDDD;
    font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
    font-size: 1.3vw;
    /* line-height: 20px; */
	}
.trip-block {
    display: none;
    background: #333;
    color: #DDD;
    padding: 0.5%;
    position: absolute;
    text-align: left;
    /* min-width: 100px; */
    border-radius: 3px;

    -webkit-box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
       -moz-box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
        -ms-box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
         -o-box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
            box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
}

.trip-arrow {
    position: absolute;
    background: none;
    background: url("arrow.png");
    z-index: 1;
}
@media only screen and (max-width: 600px){
    /* CSS that should be displayed if width is equal to or less than 991px goes here */
    .trip-arrow.n
    {
        height: 8px;
        bottom: -6px !important;
        left: 15%;
        right: 15px;
        background-repeat: no-repeat;
        background-position: 50% 100%;
        /* width: 20px; */
        background-size: 32px;
        /* background-color: red; */
    }
    .trip-arrow.e {
        width: 8px;
        height: 10px !important;
        left: -6px !important;
        top: 4px !important;
        bottom: 15px;
        background-repeat: no-repeat;
        background-position: 0% 50%;
    }
    .trip-arrow.w {
        width: 8px;
        height: 10px !important;
        right: -6px !important;
        top: 1px !important;
        bottom: 15px;
        background-repeat: no-repeat;
        background-position: 100% 50%;
    }
}
.trip-arrow.n {
    height: 8px;
    bottom: -8px;
    left: 15px;
    right: 15px;
    background-repeat: no-repeat;
    background-position: 50% 100%;
}

.trip-arrow.s {
    height: 8px;
    top: -8px;
    left: 15px;
    right: 15px;
    background-repeat: no-repeat;
    background-position: 50% 0%;
}

.trip-arrow.e {
    width: 8px;
    height: 16px;
    left: -8px;
    top: 11px;
    bottom: 15px;
    background-repeat: no-repeat;
    background-position: 0% 50%;
}

.trip-arrow.w {
    width: 8px;
    height: 16px;
    right: -8px;
    top: 11px;
    bottom: 15px;
    background-repeat: no-repeat;
    background-position: 100% 50%;
}

.trip-progress-wrapper {

}

.trip-progress-bar {
    height: 1px;
    background-color: #444;
    width: 0;
}

.trip-overlay {
    display: none;
    position: absolute;
    left: 0;
    top: 0;
    background: black;
    width: 100%;
    height: 100%;
    opacity: 0.7;
    filter: alpha(opacity = 70);
}

/*
 *  TODO:
 *  implement with more details later
 */
.trip-exposed { 
    background: white
}
