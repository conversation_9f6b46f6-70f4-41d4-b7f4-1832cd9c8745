msgid ""
msgstr ""
"Project-Id-Version: hf\\n\\n\\n\n"
"POT-Creation-Date: 2022-04-20 12:38+0530\n"
"PO-Revision-Date: 2022-04-20 12:40+0530\n"
"Last-Translator: \n"
"Language-Team: h<h>\\n\n"
"Language: en_IN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.0.1\n"
"X-Poedit-Basepath: ../../js\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: simcontrols.js\n"

#: simcontrols.js:4
msgid "HELP"
msgstr ""

#: simcontrols.js:5
msgid "Convex Lens-Image formation and Magnification"
msgstr "Convex Lens-Image formation and Magnification"

#: simcontrols.js:6
msgid ""
"Developed by Amrita University Under research grant from <br> Department of "
"Electronics & Information Technology"
msgstr ""
"Developed by Amrita University Under research grant from <br> Department of "
"Electronics & Information Technology"

#: simcontrols.js:7
msgid "Select the focal length f:"
msgstr "Select the focal length f:"

#: simcontrols.js:8
msgid "Distance between <br> object and lens u:"
msgstr "Distance between <br> object and lens u:"

#: simcontrols.js:9 simcontrols.js:11 simcontrols.js:15
msgid "cm"
msgstr "cm"

#: simcontrols.js:10
msgid "Distance between <br> lens and screen v:"
msgstr "Distance between <br> lens and screen v:"

#: simcontrols.js:12
msgid "Show Result"
msgstr "Show Result"

#: simcontrols.js:13
msgid "Magnification M:"
msgstr "Magnification M:"

#: simcontrols.js:14
msgid "Height H:"
msgstr "Height H:"

#: simcontrols.js:16
msgid "Select the focal length of convex lens."
msgstr "Select the focal length of convex lens."

#: simcontrols.js:16
msgid "Select the distance between object and lens."
msgstr "Select the distance between object and lens."

#: simcontrols.js:16
msgid "Select the distance between lens and screen."
msgstr "Select the distance between lens and screen."

#: simcontrols.js:16
msgid ""
"When image is formed,<br> click on the checkbox to show the <br> height and "
"magnification."
msgstr ""
"When image is formed,<br> click on the checkbox to show the <br> height and "
"magnification."

#: simcontrols.js:20
msgid "15 cm"
msgstr "15 cm"

#: simcontrols.js:20
msgid "16 cm"
msgstr "16 cm"

#: simcontrols.js:20
msgid "17 cm"
msgstr "17 cm"

#: simcontrols.js:20
msgid "18 cm"
msgstr "18 cm"

#: simcontrols.js:20
msgid "19 cm"
msgstr "19 cm"

#: simcontrols.js:20
msgid "20 cm"
msgstr "20 cm"

#: simcontrols.js:26 simcontrols.js:27 simcontrols.js:33 simcontrols.js:34
#: simcontrols.js:40 simcontrols.js:41
msgid " cm"
msgstr " cm"

#: simcontrols.js:44
msgid "Height of the candle flame : 2cm"
msgstr "Height of the candle flame : 2cm"

#: simcontrols.js:45
msgid "50 cm"
msgstr "50 cm"

#: simcontrols.js:46
msgid "F"
msgstr "F"

#: simcontrols.js:47
msgid "2F"
msgstr "2F"

#: simcontrols.js:122
msgid "Image at infinity"
msgstr "Image at infinity"

#: simcontrols.js:129
msgid "Image at large distance"
msgstr "Image at large distance"

#: simcontrols.js:135
msgid "Virtual image <br/> is formed"
msgstr "Virtual image <br/> is formed"

#: simcontrols.js:159 simcontrols.js:160
msgid "Highly enlarged"
msgstr "Highly enlarged"

#: simcontrols.js:169 simcontrols.js:173
msgid "Real and inverted image"
msgstr "Real and inverted image"

#: simcontrols.js:176
msgid "Virtual and erect image"
msgstr "Virtual and erect image"
