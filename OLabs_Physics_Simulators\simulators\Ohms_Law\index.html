<!DOCTYPE HTML>
<html>

<!-- Mirrored from amrita.olabs.edu.in/olab/html5/?sub=PHY&cat=EMM&exp=OhmsLaw&tempId=olab_ot&linktoken=d9a60cc25fdaee683a2160ec6f72de1b&elink_lan=kn-IN&elink_title=%e0%b2%93%e0%b2%ae%e0%b3%8d%20%e0%b2%a8%20%e0%b2%a8%e0%b2%bf%e0%b2%af%e0%b2%ae by HTTrack Website Copier/3.x [XR&CO'2014], Sat, 04 Nov 2023 16:51:54 GMT -->
<!-- AmritaCREATE 2023 --><meta http-equiv="content-type" content="text/html;charset=UTF-8" /><!-- /Added by AmritaCREATE 2023 -->
<head>
<!-- Enable IE9 Standards mode -->
<meta http-equiv="X-UA-Compatible" content="IE=9" >
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title></title>
<link rel="stylesheet" type="text/css" media="all" href="../../common/css/font.css" />
<link rel="stylesheet" type="text/css" media="all" href="../../common/css/reset.css" />
<link rel="stylesheet" type="text/css" media="all" href="../../common/css/grid.css" />
<link href="../../common/css/mainstyle.css" rel="stylesheet" type="text/css" />
<link href="../../common/js/nanoscroller/nanoscroller.css" rel="stylesheet" type="text/css" />
<script src="../../common/js/nanoscroller/overthrow.min.js"></script>
<script src="../../common/js/jquery/1.7/jquery-1.7.1.js"></script>
<script src="../../common/js/nanoscroller/jquery.nanoscroller.js"></script>
<script type="text/javascript" src="../../common/js/nanoscroller/touchready.js"></script>
<script type="text/javascript" src="../../common/js/nanoscroller/main.js"></script>
<!--[if IE]><script src="../../common/js/excanvas.js"></script><![endif]-->
<link  href="css/simstyle.css" rel="stylesheet" type="text/css" />
<link  href="../../common/js/trip/trip.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" language="javascript">
	var simPath="index.html";
</script>
<script language="javascript" type="text/javascript" src="../../common/js/jquery.i18n.js"></script>
<script type="text/javascript" language="javascript" src="../../common/js/jquery/ui/1.10.2/jquery-ui-1.10.2.custom.min.js"></script>
<script language="javascript" type="text/javascript" src="js/help.js"></script>
<script language="javascript" type="text/javascript" src="../../common/js/trip/trip.min.js"></script>
<script type="text/javascript" language="javascript" src="../../common/js/touchconvert.js"></script>
<style>
@font-face {

	font-family:digi;

	src: url(css/DS-DIGII.TTF);

}
</style>
<script language="javascript"  >
var language_script= 'kn-IN';
var language=language_script.toString();
	language=language.slice(0, 2);
	var gt=$;
	gt.gettext.setLocale(language);
</script>
<script type='text/javascript'  src='locale/kn-IN/LC_MESSAGES/language.json'></script>
<!--[if gte IE 9]>
  <style type="text/css">
    .gradient {
       filter: none;
    }
  </style>
<![endif]-->
<script src="js/js-webshim/1.9.7/minified/extras/modernizr-custom.js"></script>
<script> 
if( !Modernizr.inputtypes.range ){  
		document.write("<script type=\"text/javascript\" src=\"js/js-webshim/1.9.7/minified/polyfiller.js\"></"+"script>");
        $(document).ready(function(){
			$.webshims.setOptions("waitReady", false);
     		$.webshims.polyfill('forms-ext');
		});  
    };  


</script>

</head>
<body>
<div class="main">
<header id="silumatorTemp">
  <div class="g99 logo">
  </div>
  <!-- end .g99 -->
    <div class="g495 mainTitle">
    <p id="expName"></p>
  </div>
  <!-- end .g495 -->
    <div class="g198 menuSet">
          
  </div>
  <!-- end .g198 -->
<div class="g792 bannerFoot">
<ul id="olabmenuBar">
  	<li><a href="#">SAVE</a></li>
    <li><a id="olabFullscrBtn" onClick="parent.simFullScreenOlab()" style="cursor:pointer;" >FULLSCREEN</a></li>
    <li><a href="#">EXIT</a></li>
  </ul>
</div>
    <!-- end .grid_8 -->
</header><!-- /header -->
<div class="g594 canvasHolder"> 
    <div id="canvasBox">
<script type="text/javascript" language="javascript">
	var simPath="index.html";
</script>
<div id="bgDiv" style="background:url(images/BG.jpg) no-repeat;)"></div>
<canvas id="wireCanvas" style="border:1px solid;"></canvas>
<div id="switchKeyDiv" class="selectable">
<img id="switchKeyImg" src="images/key.png"/>
</div>
<div id="rheoKeyDiv" class="selectable"><img id="rheoKeyImg" src="images/rheostatKey.png"/></div>
<img id="batterytoKeyImg" class="selectable" src="images/batterytokey.png"/>
<img id="keytoRheostatImg" class="selectable" src="images/keytorheostat.png"/>

<img id="rheotoAmmeImg" class="selectable" src="images/rheotoamme.png"/>
<img id="voltmetertoammeterImg" class="selectable" src="images/voltmetertoammeter.png"/>
<img id="voltmetertobatteryImg" class="selectable" src="images/voltmetertobattery.png"/>
<img id="resiswireImg" src="images/resiswire.png"/>


<div id="ammeterReading"></div>
<div id="voltReading"></div>


<div id="batteryNeg"></div>
<div id="batteryPos"></div>

<div id="keyNeg"></div>
<div id="keyPos"></div>
<div id="rheoNeg"></div>
<div id="rheoPos"></div>

<div id="ammeterPos"></div>
<div id="ammeterNeg"></div>


<div id="voltNeg"></div>
<div id="voltPos"></div>
<div id="resiswireDiv" class="selectable">
           <img id="resisImg" src="images/wirewhite.png"/>
            <img id="resisShadowImg" src="images/wirewhiteshadow.png"/>
</div>

<input type="checkbox" name="circuitCheckBox" id="circuitCheckBox" onchange="showCircuit(this)">
           <p id="checkLbl" class="varTitle" style="font-size:13px;"></p> 
           <div  id="circuitdiaDiv">
           <img id="circuitdiaImg" src="images/circuitdiagram.png"/>
</div>
<br>

<script type="text/javascript" src="js/simcontrols.js"></script>

</div>
</div>
<div class="g198 controlHolder">
<div class="nano has-scrollbar">
<!--author:anishasl
Date:22/05/2014
file name:controls.php-->
<ul style="right:-17px;" tabindex="0" class="overthrow content description"><li>
		
    <div class="varBox"> 
     <p id="sliderLbl" class="varTitle" style="font-size:12px;"></p> <p id="sliderVal" class="varTitle" style="font-size:12px;"></p>   
          <input type="range" class="rangeSlider" min="0.5" max="2" id="lengthRange" name="lengthRange" value="0.5" step="0.1"  onchange="changeResisLength(this.value)"/>
          
           <p id="minVal" class="varTitle" style="font-size:13px;"></p>
             <p id="maxVal" class="varTitle" style="font-size:13px;"></p>
             
       <p id="readingHdng" class="varTitleResult"></p> 
         <p id="voltageLbl" class="varTitle" style="font-size:13px;"></p> <p id="voltMeasure" class="varTitle" style="font-size:13px;"></p>
         <p id="currentLbl" class="varTitle" style="font-size:13px;"></p> <p id="currentMeasure" class="varTitle" style="font-size:13px;"></p>
           <p id="wireLenLbl" class="varTitle"></p>        
         <p id="resultLbl" class="varTitleResult"></p>
                             <p id="inputTxtLbl1" class="varTitle" style="font-size:13px;"></p>
                             
                            
        <input type="text" class="wideTxtArea"  id="answer1" name="answer1"  disabled="disabled" onkeypress="validate(event)" />
                              <p id="ohmLabel" class="varTitle"></p> 
                              <img id="correctImg" src="images/right.png" />
                                        <img id="wrongImg" src="images/wrong.png" />

                              <p id="WarningLbl" class="varTitle"></p>
                               <p id="inputTxtLbl2" class="varTitle"></p>
                                <input type="text" class="wideTxtArea"  id="answer2" name="answer2" disabled="disabled" onkeypress="validate(event)" />
                                 <p id="ohmLabel1" class="varTitle"></p> 
                                 <img id="correctImg1" src="images/right.png" />
          <img id="wrongImg1" src="images/wrong.png" />
                                 <p id="WarningLbl1" class="varTitle"></p>
                                 <input type="button" class="subButton" disabled="disabled" id="submit" name="submit" onclick="checkAnswer()" />
                            
                        <input type="button" class="subButton scale" id="resetBtn" name="resetBtn"  onclick="window.location.reload();"  />
                  
	</div>

</li></ul>

<script type="text/javascript">
  var inputs = document.getElementsByTagName('input');
  for(var i = 0; i < inputs.length; i++) {
	  
    if(inputs[i].type == 'range') {
		inputs[i].addEventListener('click', function() {
        this.focus(); 
      });
    }
  }
  
</script>

</div>
</div>
<script type="text/javascript">
 var expTitle="Ohm's Law";
 document.getElementById("expName").innerHTML=expTitle;
</script>   <footer id="tempFooter">
 <div class="g396 footer">
<p class="labName">Developed by Amrita University Under research grant from
<br>Ministry of Electronics and Information Technology</p>  
</div>
  <!-- end .g396 -->
  <div class="g396 footer">
    <p class="copyName"></p>
  </div>
    <div class="clear"></div>
    </footer> <!-- /footer -->
</div>
</body>

<!-- Mirrored from amrita.olabs.edu.in/olab/html5/?sub=PHY&cat=EMM&exp=OhmsLaw&tempId=olab_ot&linktoken=d9a60cc25fdaee683a2160ec6f72de1b&elink_lan=kn-IN&elink_title=%e0%b2%93%e0%b2%ae%e0%b3%8d%20%e0%b2%a8%20%e0%b2%a8%e0%b2%bf%e0%b2%af%e0%b2%ae by HTTrack Website Copier/3.x [XR&CO'2014], Sat, 04 Nov 2023 16:51:54 GMT -->
</html>
<script>
function simFullScreenOlab(){
		var $ = function(selector,context){return(context||document).querySelector(selector)};
        var iframe = $("iframe"),
            domPrefixes = 'Webkit Moz O ms Khtml'.split(' ');

            var fullscreen = function(elem){
            var prefix;
			
            // Mozilla and webkit intialise fullscreen slightly differently
            for ( var i = -1, len = domPrefixes.length; ++i < len; ) 
			{
              prefix = domPrefixes[i].toLowerCase();
              if ( elem[prefix + 'EnterFullScreen'] ) {
                // Webkit uses EnterFullScreen for video
                return prefix + 'EnterFullScreen';
                break;
              } else if( elem[prefix + 'RequestFullScreen'] ) {
                // Mozilla uses RequestFullScreen for all elements and webkit uses it for non video elements
                return prefix + 'RequestFullScreen';
                break;
              }
            }
            return false;
        }; 
        // Webkit uses "requestFullScreen" for non video elements
        var fullscreenother = fullscreen(document.createElement("iframe"));
        if(!fullscreen) {
            alert("Fullscreen won't work, please make sure you're using a browser that supports it and you have enabled the feature");
            return;
        }
		iframe[fullscreenother]();
		(this,this.document);
	    }

</script>
