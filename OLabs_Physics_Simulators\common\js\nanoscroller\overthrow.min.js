/*! Overthrow v.0.1.0. An overflow:auto polyfill for responsive design. (c) 2012: <PERSON>, Filament Group, Inc. http://filamentgroup.github.com/Overthrow/license.txt */
(function(a,b){var c=a.document,d=c.documentElement,e="overthrow-enabled",f="ontouchmove"in c,g="WebkitOverflowScrolling"in d.style||!f&&a.screen.width>1200||function(){var b=a.navigator.userAgent,c=b.match(/AppleWebKit\/([0-9]+)/),d=c&&c[1],e=c&&d>=534;return b.match(/Android ([0-9]+)/)&&RegExp.$1>=3&&e||b.match(/ Version\/([0-9]+)/)&&RegExp.$1>=0&&a.blackberry&&e||b.indexOf(/PlayBook/)>-1&&RegExp.$1>=0&&e||b.match(/Fennec\/([0-9]+)/)&&RegExp.$1>=4||b.match(/wOSBrowser\/([0-9]+)/)&&RegExp.$1>=233&&e||b.match(/NokiaBrowser\/([0-9\.]+)/)&&parseFloat(RegExp.$1)===7.3&&c&&d>=533}(),h=function(a,b,c,d){return c*((a=a/d-1)*a*a+1)+b},i=!1,j,k=function(c,d){var e=0,f=c.scrollLeft,g=c.scrollTop,h={top:"+0",left:"+0",duration:100,easing:a.overthrow.easing},i,k;if(d)for(var l in h)d[l]!==b&&(h[l]=d[l]);return typeof h.left=="string"?(h.left=parseFloat(h.left),i=h.left+f):(i=h.left,h.left=h.left-f),typeof h.top=="string"?(h.top=parseFloat(h.top),k=h.top+g):(k=h.top,h.top=h.top-g),j=setInterval(function(){e++<h.duration?(c.scrollLeft=h.easing(e,f,h.left,h.duration),c.scrollTop=h.easing(e,g,h.top,h.duration)):(i!==c.scrollLeft&&(c.scrollLeft=i),k!==c.scrollTop&&(c.scrollTop=k),m())},1),{top:k,left:i,duration:h.duration,easing:h.easing}},l=function(a,b){return!b&&a.className&&a.className.indexOf("overthrow")>-1&&a||l(a.parentNode)},m=function(){clearInterval(j)},n=function(){if(i)return;i=!0;if(g||f)d.className+=" "+e;a.overthrow.forget=function(){d.className=d.className.replace(e,""),c.removeEventListener&&c.removeEventListener("touchstart",y,!1),a.overthrow.easing=h,i=!1};if(g||!f)return;var j,n=[],o=[],p,q,r=function(){n=[],p=null},s=function(){o=[],q=null},t=function(){var a=(n[0]-n[n.length-1])*8,b=(o[0]-o[o.length-1])*8,c=Math.max(Math.abs(b),Math.abs(a))/8;a=(a>0?"+":"")+a,b=(b>0?"+":"")+b,!isNaN(c)&&c>0&&(Math.abs(b)>80||Math.abs(a)>80)&&k(j,{left:b,top:a,duration:c})},u,v=function(a){u=j.querySelectorAll("textarea, input");for(var b=0,c=u.length;b<c;b++)u[b].style.pointerEvents=a},x=function(a,d){if(c.createEvent){var e=(!d||d===b)&&j.parentNode||j.touchchild||j,f;e!==j&&(f=c.createEvent("HTMLEvents"),f.initEvent("touchend",!0,!0),j.dispatchEvent(f),e.touchchild=j,j=e,e.dispatchEvent(a))}},y=function(a){m(),r(),s(),j=l(a.target);if(!j||j===d||a.touches.length>1)return;v("none");var b=a,c=j.scrollTop,e=j.scrollLeft,f=j.offsetHeight,g=j.offsetWidth,h=a.touches[0].pageY,i=a.touches[0].pageX,k=j.scrollHeight,u=j.scrollWidth,w=function(a){var d=c+h-a.touches[0].pageY,l=e+i-a.touches[0].pageX,m=d>=(n.length?n[0]:0),t=l>=(o.length?o[0]:0);d>0&&d<k-f||l>0&&l<u-g?a.preventDefault():x(b),p&&m!==p&&r(),q&&t!==q&&s(),p=m,q=t,j.scrollTop=d,j.scrollLeft=l,n.unshift(d),o.unshift(l),n.length>3&&n.pop(),o.length>3&&o.pop()},y=function(a){t(),v("auto"),setTimeout(function(){v("none")},450),j.removeEventListener("touchmove",w,!1),j.removeEventListener("touchend",y,!1)};j.addEventListener("touchmove",w,!1),j.addEventListener("touchend",y,!1)};c.addEventListener("touchstart",y,!1)};a.overthrow={set:n,forget:function(){},easing:h,toss:k,intercept:m,closest:l,support:g?"native":f&&"polyfilled"||"none"},n()})(this);