<!DOCTYPE HTML>
<html>

<!-- Mirrored from amrita.olabs.edu.in/olab/html5/?sub=PHY&cat=OPT&exp=Image_formation_and_Magnification_of_a_Convex_Lens&tempId=olab_ot&linktoken=0b4fa585acb0cd10990994f61c2584d0&elink_lan=hi-IN&elink_title=Convex%20Lens%20-%20Image%20Formation%20and%20Magnification by HTTrack Website Copier/3.x [XR&CO'2014], Sat, 04 Nov 2023 19:04:47 GMT -->
<!-- AmritaCREATE 2023 --><meta http-equiv="content-type" content="text/html;charset=UTF-8" /><!-- /Added by AmritaCREATE 2023 -->
<head>
<!-- Enable IE9 Standards mode -->
<meta http-equiv="X-UA-Compatible" content="IE=9" >
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title></title>
<link rel="stylesheet" type="text/css" media="all" href="../../common/css/font.css" />
<link rel="stylesheet" type="text/css" media="all" href="../../common/css/reset.css" />
<link rel="stylesheet" type="text/css" media="all" href="../../common/css/grid.css" />
<link href="../../common/css/mainstyle.css" rel="stylesheet" type="text/css" />
<link href="../../common/js/nanoscroller/nanoscroller.css" rel="stylesheet" type="text/css" />
<script src="../../common/js/nanoscroller/overthrow.min.js"></script>
<script src="../../common/js/jquery/1.7/jquery-1.7.1.js"></script>
<script src="../../common/js/nanoscroller/jquery.nanoscroller.js"></script>
<script type="text/javascript" src="../../common/js/nanoscroller/touchready.js"></script>
<script type="text/javascript" src="../../common/js/nanoscroller/main.js"></script>
<!--[if IE]><script src="../../common/js/excanvas.js"></script><![endif]-->
<link  href="css/simstyle.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="js/simcontrols.js"></script>
<script language="javascript" type="text/javascript" src="../../common/js/jquery/ui/1.10.2/jquery-ui-1.10.2.custom.min.js"></script>
<script type="text/javascript" language="javascript" src="../../common/js/touchconvert.js"></script>
 <script language="javascript" type="text/javascript" src="../../common/js/trip/trip.min.js"></script>
 <script language="javascript" type="text/javascript" src="js/help.js"></script>
 <link href="../../common/js/trip/trip.css" rel="stylesheet" type="text/css" />
 <script type="text/javascript" src="js/Gettext.js"></script>
 <script type="text/javascript" src="js/QuestionExecutePackage.js"></script>

<link rel="gettext" type="application/x-po" href='locale/hi-IN/messages.html' />  <!--[if gte IE 9]>
  <style type="text/css">
    .gradient {
       filter: none;
    }
  </style>
<![endif]-->
<script src="../../common/js/js-webshim/1.9.7/minified/extras/modernizr-custom.js"></script>
<script> 
if( !Modernizr.inputtypes.range ){  
		document.write("<script type=\"text/javascript\" src=\"../../common/js/js-webshim/1.9.7/minified/polyfiller.js\"></"+"script>");
        $(document).ready(function(){
			$.webshims.setOptions("waitReady", false);
     		$.webshims.polyfill('forms-ext');
		});  
    };  


</script>

</head>
<body>
<div class="main">
<header id="silumatorTemp">
  <div class="g99 logo">
  </div>
  <!-- end .g99 -->
    <div class="g495 mainTitle">
    <p id="expName"></p>
  </div>
  <!-- end .g495 -->
    <div class="g198 menuSet">
          
  </div>
  <!-- end .g198 -->
<div class="g792 bannerFoot">
<ul id="olabmenuBar">
  	<li><a href="#">SAVE</a></li>
    <li><a id="olabFullscrBtn" onClick="parent.simFullScreenOlab()" style="cursor:pointer;" >FULLSCREEN</a></li>
    <li><a href="#">EXIT</a></li>
  </ul>
</div>
    <!-- end .grid_8 -->
</header><!-- /header -->
<div class="g594 canvasHolder"> 
    <div id="canvasBox">
<div id="mainDiv">
	<img id="bg" src="images/BG.png"/>
	<img id="tableScale" src="images/Scale.png"/>
	<div id="lensDiv">
		<img id="lens" src="images/ConvexLens.png"/>
		<img id="lensPopup" class="popUp" src="images/PopUp.png"/>
		<div id="lenspopupText"></div>
	</div>
	<div id="screenDiv">
		<img id="screen" src="images/Board.png"/>
		<div id="screenPopupDiv">
			<img id="screenPopup" class="popUp" src="images/PopUp.png"/>
			<div id="screenPopupText"></div>
		</div>
		<img id="screenImg" src="images/Flame1.png"/>
	</div>
	<div id="candleDiv">
	<img id="candle1" src="images/Candle1.png" />
	<div id="candlePopupDiv">
			<img id="candlePopup" class="popUp" src="images/PopUp.png"/>
			<div id="candlepopupText"></div>
		</div>
	</div>
	<p id ="textHeight"></p>
	<div id="objectF1Div">
		<p id ="objectF1" class = "textF"></p>
		<img id="objectF1Popup" class="popUp" src="images/PopUp.png"/>
		<div id="objectF1popupText"></div>
	</div>
	<div id="object2F1Div">
		<p id ="object2F1" class = "textF"></p>
		<img id="object2F1Popup" class="popUp" src="images/PopUp.png"/>
		<div id="object2F1popupText"></div>
	</div>
	<div id="objectF2Div">
		<p id ="objectF2" class = "textF"></p>
		<img id="objectF2Popup" class="popUp" src="images/PopUp.png"/>
		<div id="objectF2popupText"></div>
	</div>
	<div id="object2F2Div">
	<p id ="object2F2" class = "textF"></p>
	<img id="object2F2Popup" class="popUp" src="images/PopUp.png"/>
		<div id="object2F2popupText"></div>
	</div>
	<div id="rightArrowDiv">
		<p class="arrowText"></p>
		<img src="images/Arrow.png"/>
	</div>
	<div id="leftArrowDiv">
		<p class="arrowText"></p>
		<img id="Arrow" src="images/Arrow.png"/>
	</div>
	<img id="inference" src="images/Inference.png"/>
</div>
</div>
</div>
<div class="g198 controlHolder">
<ul style="right: -17px;" tabindex="0" class="overthrow content description">
	<li>
		<div class="varBox"> 
				<p class="varTitle"></p>
				<select id="DropboxTest" class="dropBox"  ></select>
				<br />
				<div id ="objectLensDiv">
					<p class="varTitle"></p>
					<p id="objectDis"></p>
					<p></p>
				</div>
				<input class="rangeSlider" type="range" style="margin: 3px 0px 0px 16px;" min="10" max="50" id="ObjectLensDis"  value="50" step="1" oninput="ChangeObjectDistance(this.value)"/>
				<span style="margin: 0px 25px 0px 20px; " id="leftminval">10</span>
				<span style="margin: 0px 0px 0px 76px;  " id="leftmaxval">50</span>
				<br /><br />
				<div id="lensScreenDiv">
					<p class="varTitle"></p>
					<p id="screenDis"></p>
					<p> </p>
				</div>
				<input class="rangeSlider" type="range" style="margin: 3px 0px 0px 16px;" min="20" max="100" id="LensScreenDis"  value="100" step="1" oninput="ChangeScreenDistance(this.value)"/>
				<span style="margin: 0px 25px 0px 20px; " id="leftminval">20</span>
				<span style="margin: 0px 0px 0px 78px;  " id="leftmaxval">100</span>
				 <!-- <p class="varTitle">
					 <input type="checkbox" id="resultBox"  disabled>
					<label></label><br>
				</p> -->
				<!-- <br /> -->
				<!-- <div id="resultMagnificationDiv">
					<p class="varTitle"></p>
					<p id="resultMagnification"></p>
				</div>
		
				<div id="resultHeightDiv">
					<p class="varTitle"></p>
					<p id="resultHeight"></p>
					<p></p>
					<br/>
					<p class="varTitle" id="resultText"></p>
				</div> -->
				
				
				<br /><br />
				<p align="center">
					<input type="button" class="subButton" id="reset" name="reset" value="Reset" onclick="resetFN()" style="margin-top: 33%;"/>
				</p>
		</div>
	</li>
</ul>
</div>
<script type="text/javascript">
 var expTitle="Convex Lens-Image formation and Magnification";
 document.getElementById("expName").innerHTML=expTitle;
</script>   <footer id="tempFooter">
 <div class="g396 footer">
<p class="labName">Developed by Amrita University Under research grant from
<br>Ministry of Electronics and Information Technology</p>  
</div>
  <!-- end .g396 -->
  <div class="g396 footer">
    <p class="copyName"></p>
  </div>
    <div class="clear"></div>
    </footer> <!-- /footer -->
</div>
</body>

<!-- Mirrored from amrita.olabs.edu.in/olab/html5/?sub=PHY&cat=OPT&exp=Image_formation_and_Magnification_of_a_Convex_Lens&tempId=olab_ot&linktoken=0b4fa585acb0cd10990994f61c2584d0&elink_lan=hi-IN&elink_title=Convex%20Lens%20-%20Image%20Formation%20and%20Magnification by HTTrack Website Copier/3.x [XR&CO'2014], Sat, 04 Nov 2023 19:04:47 GMT -->
</html>
<script>
function simFullScreenOlab(){
		var $ = function(selector,context){return(context||document).querySelector(selector)};
        var iframe = $("iframe"),
            domPrefixes = 'Webkit Moz O ms Khtml'.split(' ');

            var fullscreen = function(elem){
            var prefix;
			
            // Mozilla and webkit intialise fullscreen slightly differently
            for ( var i = -1, len = domPrefixes.length; ++i < len; ) 
			{
              prefix = domPrefixes[i].toLowerCase();
              if ( elem[prefix + 'EnterFullScreen'] ) {
                // Webkit uses EnterFullScreen for video
                return prefix + 'EnterFullScreen';
                break;
              } else if( elem[prefix + 'RequestFullScreen'] ) {
                // Mozilla uses RequestFullScreen for all elements and webkit uses it for non video elements
                return prefix + 'RequestFullScreen';
                break;
              }
            }
            return false;
        }; 
        // Webkit uses "requestFullScreen" for non video elements
        var fullscreenother = fullscreen(document.createElement("iframe"));
        if(!fullscreen) {
            alert("Fullscreen won't work, please make sure you're using a browser that supports it and you have enabled the feature");
            return;
        }
		iframe[fullscreenother]();
		(this,this.document);
	    }

</script>
