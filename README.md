# MeterBridge Simulator - Equivalent Resistance of Resistors (Parallel)

## Overview
This is a standalone HTML5 physics simulator for studying the equivalent resistance of resistors in parallel configuration using a Meter Bridge (Wheatstone Bridge) setup. The simulator was extracted from the Amrita Virtual Labs (OLabs) educational platform.

---

# 🔥 BEGINNER'S GUIDE: How to Extract ANY Simulator from OLabs

## 📚 What You Need to Know First

**OLabs** is like a big library of science experiments. Each experiment is a "simulator" - a web page that lets you do virtual experiments. Think of it like a video game, but for learning science!

**Our Goal**: Take one simulator out of this big library and make it work by itself, like taking one book out of a library and making sure you have everything needed to read it.

---

## 🎯 STEP-BY-STEP EXTRACTION GUIDE

### **STEP 1: Find Your Target Simulator**

**What to do**: Look for the main HTML file of the simulator you want.

**Example from our project**:
```
📁 htdocs/amrita.olabs.edu.in/olab/html5/
   📄 index2a0a.html  ← This is our target file!
```

**How to identify it**:
- Look for files named like `index[something].html`
- These are usually in the `html5` folder
- Each file represents one complete simulator

**Real Example**:
- File: `index2a0a.html`
- This contains the MeterBridge simulator

---

### **STEP 2: Open and Study the HTML File**

**What to do**: Open the HTML file and look for what it needs to work.

**Example from our MeterBridge simulator**:
```html
<!-- CSS Files it needs -->
<link href="template/olab/css/reset.css" rel="stylesheet" />
<link href="template/olab/css/grid.css" rel="stylesheet" />
<link href="template/olab/css/mainstyle.css" rel="stylesheet" />
<link href="../PHY/EMM/MeterBridge-Single-And-Parallel/css/simstyle.css" rel="stylesheet" />

<!-- JavaScript Files it needs -->
<script src="js/jquery-latest.js"></script>
<script src="js/jquery.i18n.js"></script>
<script src="../PHY/EMM/MeterBridge-Single-And-Parallel/js/simcontrols.js"></script>

<!-- Images it needs -->
<img src="../PHY/EMM/MeterBridge-Single-And-Parallel/images/connector.png" />
<img src="../PHY/EMM/MeterBridge-Single-And-Parallel/images/battery.png" />

<!-- Language files it needs -->
<script src='../PHY/EMM/MeterBridge-Single-And-Parallel/locale/en-IN/language.json'></script>
```

**What this tells us**:
- **CSS**: Styling files (how it looks)
- **JavaScript**: Logic files (how it works)
- **Images**: Picture files (what you see)
- **Locale**: Language files (different languages)

---

### **STEP 3: Understand the File Paths**

**File paths are like addresses**. Let's decode them:

**Template files** (shared by all simulators):
```
template/olab/css/reset.css
↓
📁 htdocs/amrita.olabs.edu.in/olab/html5/template/olab/css/reset.css
```

**Experiment-specific files**:
```
../PHY/EMM/MeterBridge-Single-And-Parallel/css/simstyle.css
↓
📁 htdocs/amrita.olabs.edu.in/olab/PHY/EMM/MeterBridge-Single-And-Parallel/css/simstyle.css
```

**Breaking it down**:
- `..` means "go up one folder"
- `PHY` = Physics subject
- `EMM` = Electromagnetism category
- `MeterBridge-Single-And-Parallel` = Experiment name

---

### **STEP 4: Create Your New Folder Structure**

**What to do**: Make a clean, organized folder for your simulator.

**Our example structure**:
```
📁 MeterBridge_Simulator/          ← Main folder
   📄 index.html                   ← Main file (renamed)
   📁 css/                         ← All style files
      📄 reset.css
      📄 grid.css
      📄 mainstyle.css
      📄 simstyle.css
   📁 js/                          ← All script files
      📄 jquery-latest.js
      📄 jquery.i18n.js
      📄 simcontrols.js
      📁 js-webshim/
   📁 images/                      ← All picture files
      📄 battery.png
      📄 resistor.png
      📄 connector.png
      📄 [46 more images...]
   📁 locale/                      ← All language files
      📁 en-IN/
         📄 language.json
      📁 hi-IN/
      📁 [3 more languages...]
   📄 README.md                    ← This guide!
```

**Why this structure?**:
- **Simple**: Everything is organized in clear folders
- **Standalone**: No complex paths, everything is local
- **Clean**: Easy to understand and maintain

---

### **STEP 5: Copy All the Files**

**What to do**: Copy all the files your simulator needs into your new folders.

**Template files** (copy these for EVERY simulator):
```bash
# CSS files (how it looks)
📁 htdocs/amrita.olabs.edu.in/olab/html5/template/olab/css/
   📄 reset.css     → copy to → 📁 YourSimulator/css/reset.css
   📄 grid.css      → copy to → 📁 YourSimulator/css/grid.css
   📄 mainstyle.css → copy to → 📁 YourSimulator/css/mainstyle.css

# JavaScript files (how it works)
📁 htdocs/amrita.olabs.edu.in/olab/html5/js/
   📄 jquery-latest.js → copy to → 📁 YourSimulator/js/jquery-latest.js
   📄 jquery.i18n.js   → copy to → 📁 YourSimulator/js/jquery.i18n.js

# Template images (logos, backgrounds)
📁 htdocs/amrita.olabs.edu.in/olab/html5/template/olab/images/
   📄 *.gif, *.png → copy to → 📁 YourSimulator/images/
```

**Experiment-specific files** (different for each simulator):
```bash
# For MeterBridge example:
📁 htdocs/amrita.olabs.edu.in/olab/PHY/EMM/MeterBridge-Single-And-Parallel/

# CSS files
📁 css/simstyle.css → copy to → 📁 YourSimulator/css/simstyle.css

# JavaScript files
📁 js/simcontrols.js → copy to → 📁 YourSimulator/js/simcontrols.js

# Images
📁 images/*.png → copy to → 📁 YourSimulator/images/

# Languages
📁 locale/ → copy to → 📁 YourSimulator/locale/
```

---

### **STEP 6: Update File Paths in HTML**

**What to do**: Change all the file paths in your HTML to point to your new folders.

**Before** (original paths):
```html
<link href="template/olab/css/reset.css" rel="stylesheet" />
<link href="../PHY/EMM/MeterBridge-Single-And-Parallel/css/simstyle.css" rel="stylesheet" />
<script src="../PHY/EMM/MeterBridge-Single-And-Parallel/js/simcontrols.js"></script>
<img src="../PHY/EMM/MeterBridge-Single-And-Parallel/images/battery.png" />
```

**After** (new simple paths):
```html
<link href="css/reset.css" rel="stylesheet" />
<link href="css/simstyle.css" rel="stylesheet" />
<script src="js/simcontrols.js"></script>
<img src="images/battery.png" />
```

**The pattern**:
- `template/olab/css/` → `css/`
- `../PHY/EMM/[EXPERIMENT]/css/` → `css/`
- `../PHY/EMM/[EXPERIMENT]/js/` → `js/`
- `../PHY/EMM/[EXPERIMENT]/images/` → `images/`
- `../PHY/EMM/[EXPERIMENT]/locale/` → `locale/`

---

### **STEP 7: Update File Paths in JavaScript**

**What to do**: Open the JavaScript files and update image paths there too.

**Example from simcontrols.js**:

**Before**:
```javascript
img.src='../../olab/PHY/EMM/MeterBridge-Single-And-Parallel/images/MeterBridge.png';
batteryImg.src='../../olab/PHY/EMM/MeterBridge-Single-And-Parallel/images/battery.png';
```

**After**:
```javascript
img.src='images/MeterBridge.png';
batteryImg.src='images/battery.png';
```

**How to find these**:
1. Open the JavaScript file in a text editor
2. Search for `.png`, `.jpg`, `.gif`
3. Replace the long paths with simple `images/filename.png`

---

### **STEP 8: Test Your Simulator**

**What to do**: Open your HTML file and check if everything works.

**How to test**:
1. Double-click `index.html` to open in browser
2. Check if all images load (no broken image icons)
3. Check browser console (F12) for errors
4. Try clicking buttons and interacting with the simulator

**Common problems and fixes**:
- **Broken images**: Check if image files are copied and paths are correct
- **JavaScript errors**: Check if JS files are copied and paths are correct
- **Missing styles**: Check if CSS files are copied and paths are correct

---

## 📋 QUICK CHECKLIST FOR ANY SIMULATOR

### ✅ **Before You Start**:
- [ ] Find the main HTML file (usually `index[something].html`)
- [ ] Create your new folder structure
- [ ] Have a text editor ready (Notepad++, VS Code, etc.)

### ✅ **Files to Copy** (for every simulator):
- [ ] **Template CSS**: `reset.css`, `grid.css`, `mainstyle.css`
- [ ] **Template JS**: `jquery-latest.js`, `jquery.i18n.js`
- [ ] **Template Images**: All `.gif` and `.png` from template folder
- [ ] **Experiment CSS**: Usually one `simstyle.css` file
- [ ] **Experiment JS**: Usually one `simcontrols.js` file
- [ ] **Experiment Images**: All `.png` files from experiment folder
- [ ] **Language Files**: All `locale` folders and `language.json` files

### ✅ **Paths to Update**:
- [ ] **HTML file**: Update all `<link>`, `<script>`, and `<img>` tags
- [ ] **JavaScript files**: Update all image paths inside `.js` files
- [ ] **Test**: Open in browser and check for errors

---

## 🎯 REAL EXAMPLE: Our MeterBridge Simulator

### **What we found**:
```
📄 Original file: htdocs/amrita.olabs.edu.in/olab/html5/index2a0a.html
📁 Experiment path: htdocs/amrita.olabs.edu.in/olab/PHY/EMM/MeterBridge-Single-And-Parallel/
```

### **What we created**:

```
📁 MeterBridge_Simulator/          ← Clean, organized folder
   📄 index.html                   ← Renamed from index2a0a.html
   📁 css/                         ← 4 CSS files
      📄 reset.css                 ← From template
      📄 grid.css                  ← From template
      📄 mainstyle.css             ← From template
      📄 simstyle.css              ← From experiment
   📁 js/                          ← 3 main JS files + polyfills
      📄 jquery-latest.js          ← From template
      📄 jquery.i18n.js            ← From template
      📄 simcontrols.js            ← From experiment
      📁 js-webshim/               ← Browser compatibility
   📁 images/                      ← 46 image files
      📄 MeterBridge.png           ← Main circuit image
      📄 battery.png               ← Component images
      📄 resistor.png              ← Component images
      📄 [43 more images...]       ← All other images
   📁 locale/                      ← 5 languages
      📁 en-IN/, hi-IN/, kn-IN/, ml-IN/, mr-IN/
   📄 README.md                    ← This guide!
```

### **Files we copied**:
- **4 CSS files**: For styling and layout
- **3 JavaScript files**: For functionality and interactions
- **46 image files**: Every single image the simulator uses
- **5 language packs**: For multi-language support
- **1 HTML file**: The main simulator (renamed for clarity)

### **Paths we updated**:
- **25 paths in HTML**: All links, scripts, and images
- **18 paths in JavaScript**: All image references in code
- **Result**: Everything works perfectly!

---

## 🚀 HOW TO RUN YOUR EXTRACTED SIMULATOR

### **Method 1: Simple (Double-click)**
```
1. Go to your simulator folder
2. Double-click index.html
3. It opens in your browser
4. Start experimenting!
```

### **Method 2: Web Server (Better)**
```bash
# If you have Python installed:
cd YourSimulator
python -m http.server 8000

# Then open: http://localhost:8000
```

**Why use a web server?**
- Some features work better with a web server
- More like the real website experience
- Better for testing

---

## 🔍 FINDING OTHER SIMULATORS

### **Common Simulator Locations**:
```
📁 htdocs/amrita.olabs.edu.in/olab/html5/
   📄 index[hash].html  ← Look for files like this

📁 htdocs/amrita.olabs.edu.in/olab/[SUBJECT]/[CATEGORY]/[EXPERIMENT]/
   📁 css/              ← Experiment styles
   📁 js/               ← Experiment logic
   📁 images/           ← Experiment images
   📁 locale/           ← Experiment languages
```

### **Subject Codes**:
- **PHY** = Physics
- **CHE** = Chemistry
- **BIO** = Biology
- **CSE** = Computer Science
- **EEE** = Electrical Engineering

### **Example Experiments**:
```
📁 PHY/EMM/MeterBridge-Single-And-Parallel/     ← Our example
📁 PHY/MEC/Pendulum-Simple/                     ← Simple pendulum
📁 CHE/ACH/Titration-Acid-Base/                 ← Acid-base titration
📁 BIO/BOT/Photosynthesis-Light/                ← Photosynthesis
```

---

## ⚠️ COMMON MISTAKES TO AVOID

### **❌ Don't Do This**:
1. **Forget template files**: Every simulator needs the template CSS/JS
2. **Miss image files**: Check both template and experiment image folders
3. **Skip JavaScript paths**: Images in JS files need updating too
4. **Ignore language files**: Copy all locale folders for full functionality
5. **Use wrong paths**: Make sure paths match your new folder structure

### **✅ Do This Instead**:
1. **Copy everything**: Better to have extra files than missing ones
2. **Test frequently**: Check your simulator after each major step
3. **Use simple paths**: `css/`, `js/`, `images/`, `locale/`
4. **Keep organized**: Clean folder structure makes debugging easier
5. **Document changes**: Note what you modified for future reference

---

## 🎓 UNDERSTANDING THE OLABS STRUCTURE

### **How OLabs is Organized**:
```
📁 OLabs Project
   📁 html5/                    ← Main simulator files
      📄 index[hash].html       ← Individual simulators
      📁 template/              ← Shared files (CSS, JS, images)
      📁 js/                    ← Shared JavaScript libraries
   📁 [SUBJECT]/                ← Subject-specific content
      📁 [CATEGORY]/            ← Category within subject
         📁 [EXPERIMENT]/       ← Individual experiment files
            📁 css/             ← Experiment styles
            📁 js/              ← Experiment logic
            📁 images/          ← Experiment images
            📁 locale/          ← Experiment languages
```

### **Why This Structure Exists**:
- **Shared files**: Template files are used by all simulators
- **Organized content**: Each subject/experiment has its own folder
- **Reusability**: Common libraries (jQuery) are shared
- **Maintenance**: Easy to update shared components

### **What We Do**:
- **Flatten the structure**: Put everything in simple folders
- **Make it standalone**: No dependencies on other folders
- **Keep it organized**: Logical folder names (css, js, images, locale)

---

## 💡 TIPS FOR BEGINNERS

### **Start Small**:
1. **Pick a simple simulator first**: Look for ones with fewer images
2. **Practice the process**: Do 2-3 extractions to get comfortable
3. **Keep notes**: Write down what works for you
4. **Ask for help**: Don't be afraid to ask questions

### **Tools That Help**:
- **Text Editor**: Notepad++, VS Code, or even regular Notepad
- **File Manager**: Windows Explorer, or any file browser
- **Web Browser**: Chrome, Firefox (with Developer Tools - press F12)
- **Image Viewer**: To check if images are working

### **Learning Path**:
1. **Start**: Extract one simple simulator following this guide
2. **Practice**: Try 2-3 more simulators to get comfortable
3. **Understand**: Learn what each file type does (CSS, JS, images)
4. **Improve**: Start customizing simulators for your needs

---

## 🎯 WHAT EACH FILE TYPE DOES

### **HTML Files** (.html):
- **What**: The main structure of the simulator
- **Like**: The skeleton of a house
- **Contains**: Layout, text, links to other files

### **CSS Files** (.css):
- **What**: How the simulator looks (colors, sizes, positions)
- **Like**: Paint and decorations for the house
- **Contains**: Styling rules, colors, fonts, layouts

### **JavaScript Files** (.js):
- **What**: How the simulator works (interactions, calculations)
- **Like**: The electrical wiring and plumbing of the house
- **Contains**: Code that makes things move and respond

### **Image Files** (.png, .gif, .jpg):
- **What**: Pictures used in the simulator
- **Like**: Furniture and decorations in the house
- **Contains**: Circuit diagrams, component images, backgrounds

### **Language Files** (.json):
- **What**: Text in different languages
- **Like**: Instruction manuals in different languages
- **Contains**: All text that appears in the simulator

---

## 🔧 TROUBLESHOOTING FOR BEGINNERS

### **Problem**: "Images not showing (broken image icons)"
**Solution**:
1. Check if image files are in the `images/` folder
2. Check if HTML file has correct paths: `src="images/filename.png"`
3. Make sure image filenames match exactly (case-sensitive)

### **Problem**: "Simulator looks wrong (no colors/styling)"
**Solution**:
1. Check if CSS files are in the `css/` folder
2. Check if HTML file links to CSS: `href="css/filename.css"`
3. Make sure all 4 CSS files are copied

### **Problem**: "Simulator doesn't work (no interactions)"
**Solution**:
1. Check if JavaScript files are in the `js/` folder
2. Check if HTML file links to JS: `src="js/filename.js"`
3. Press F12 in browser and check for error messages

### **Problem**: "Can't find the main HTML file"
**Solution**:
1. Look in `htdocs/amrita.olabs.edu.in/olab/html5/`
2. Search for files starting with `index`
3. Look for files ending with `.html`

### **Problem**: "Don't know which experiment folder to use"
**Solution**:
1. Open the HTML file in a text editor
2. Look for paths like `../PHY/EMM/SomeName/`
3. That `SomeName` is your experiment folder

---

## 📚 EXAMPLE: Step-by-Step for Beginners

Let's say you want to extract a **Simple Pendulum** simulator:

### **Step 1**: Find the HTML file
```
📁 htdocs/amrita.olabs.edu.in/olab/html5/
   📄 index3b2c.html  ← Found it! (example name)
```

### **Step 2**: Open and look inside
```html
<!-- You'll see paths like this: -->
<link href="../PHY/MEC/Pendulum-Simple/css/simstyle.css" />
<script src="../PHY/MEC/Pendulum-Simple/js/simcontrols.js"></script>
<img src="../PHY/MEC/Pendulum-Simple/images/pendulum.png" />
```

### **Step 3**: Understand the experiment path
```
../PHY/MEC/Pendulum-Simple/
↓
📁 htdocs/amrita.olabs.edu.in/olab/PHY/MEC/Pendulum-Simple/
```

### **Step 4**: Create your folder
```
📁 Pendulum_Simulator/
   📁 css/
   📁 js/
   📁 images/
   📁 locale/
```

### **Step 5**: Copy files
```bash
# Template files (same for all simulators)
📁 template/olab/css/ → 📁 Pendulum_Simulator/css/
📁 html5/js/jquery-latest.js → 📁 Pendulum_Simulator/js/

# Experiment files (specific to pendulum)
📁 PHY/MEC/Pendulum-Simple/css/ → 📁 Pendulum_Simulator/css/
📁 PHY/MEC/Pendulum-Simple/js/ → 📁 Pendulum_Simulator/js/
📁 PHY/MEC/Pendulum-Simple/images/ → 📁 Pendulum_Simulator/images/
📁 PHY/MEC/Pendulum-Simple/locale/ → 📁 Pendulum_Simulator/locale/
```

### **Step 6**: Update paths in HTML
```html
<!-- Change this: -->
<link href="../PHY/MEC/Pendulum-Simple/css/simstyle.css" />

<!-- To this: -->
<link href="css/simstyle.css" />
```

### **Step 7**: Test it!
```
Double-click index.html → Should work!
```

---

## 🎉 CONGRATULATIONS!

If you followed this guide, you now know how to:
- ✅ Find any simulator in the OLabs project
- ✅ Understand what files it needs to work
- ✅ Create a clean, organized folder structure
- ✅ Copy all the necessary files
- ✅ Update file paths to make it standalone
- ✅ Test and troubleshoot your extracted simulator

**You're now ready to extract any simulator from OLabs!** 🚀

Remember: Practice makes perfect. Start with simple simulators and work your way up to more complex ones.

---

## 📞 NEED HELP?

If you get stuck:
1. **Read this guide again** - sometimes a second read helps
2. **Check the example** - compare your work with our MeterBridge example
3. **Use browser tools** - Press F12 to see error messages
4. **Start over** - sometimes it's easier to start fresh
5. **Ask questions** - don't be afraid to ask for help!

**Good luck with your simulator extraction journey!** 🌟

## How to Extract Other Simulators

To extract other simulators from the OLabs platform, follow this systematic approach:

### Step 1: Identify the Simulator
1. Locate the main HTML file (usually named `index[hash].html`)
2. Note the experiment path structure: `../[SUBJECT]/[CATEGORY]/[EXPERIMENT-NAME]/`

### Step 2: Analyze Dependencies
Examine the HTML file to identify:
- **CSS files**: Look for `<link>` tags pointing to stylesheets
- **JavaScript files**: Look for `<script>` tags with `src` attributes
- **Images**: Look for `<img>` tags and CSS `background-image` properties
- **Language files**: Look for locale/language JSON files

### Step 3: Create Folder Structure
```bash
mkdir -p "SimulatorName/css" "SimulatorName/js" "SimulatorName/images" "SimulatorName/locale"
```

### Step 4: Copy Dependencies
```bash
# Template CSS files (common to all simulators)
cp "htdocs/amrita.olabs.edu.in/olab/html5/template/olab/css/"* "SimulatorName/css/"

# Experiment-specific CSS
cp "htdocs/amrita.olabs.edu.in/olab/[SUBJECT]/[CATEGORY]/[EXPERIMENT]/css/"* "SimulatorName/css/"

# JavaScript files
cp "htdocs/amrita.olabs.edu.in/olab/html5/js/jquery-latest.js" "SimulatorName/js/"
cp "htdocs/amrita.olabs.edu.in/olab/html5/js/jquery.i18n.js" "SimulatorName/js/"
cp -r "htdocs/amrita.olabs.edu.in/olab/html5/js/js-webshim" "SimulatorName/js/"

# Experiment-specific JavaScript
cp "htdocs/amrita.olabs.edu.in/olab/[SUBJECT]/[CATEGORY]/[EXPERIMENT]/js/"* "SimulatorName/js/"

# Images
cp -r "htdocs/amrita.olabs.edu.in/olab/[SUBJECT]/[CATEGORY]/[EXPERIMENT]/images/"* "SimulatorName/images/"
cp -r "htdocs/amrita.olabs.edu.in/olab/html5/template/olab/images/"* "SimulatorName/images/"

# Language files
cp -r "htdocs/amrita.olabs.edu.in/olab/[SUBJECT]/[CATEGORY]/[EXPERIMENT]/locale/"* "SimulatorName/locale/"
```

### Step 5: Update File Paths
Edit the HTML file to update all relative paths:
- `template/olab/css/` → `css/`
- `../[SUBJECT]/[CATEGORY]/[EXPERIMENT]/css/` → `css/`
- `js/` → `js/` (usually already correct)
- `../[SUBJECT]/[CATEGORY]/[EXPERIMENT]/js/` → `js/`
- `../[SUBJECT]/[CATEGORY]/[EXPERIMENT]/images/` → `images/`
- `../[SUBJECT]/[CATEGORY]/[EXPERIMENT]/locale/` → `locale/`

### Step 6: Test and Debug
1. Open the HTML file in a browser
2. Check browser console for any missing file errors
3. Verify all images load correctly
4. Test simulator functionality

## Common Path Patterns in OLabs

- **Template files**: `htdocs/amrita.olabs.edu.in/olab/html5/template/olab/`
- **JavaScript libraries**: `htdocs/amrita.olabs.edu.in/olab/html5/js/`
- **Experiment files**: `htdocs/amrita.olabs.edu.in/olab/[SUBJECT]/[CATEGORY]/[EXPERIMENT]/`

Where:
- `[SUBJECT]` = PHY (Physics), CHE (Chemistry), BIO (Biology), etc.
- `[CATEGORY]` = EMM (Electromagnetism), MEC (Mechanics), etc.
- `[EXPERIMENT]` = Specific experiment name

## Troubleshooting

### Common Issues:
1. **Missing images**: Check if all image paths are updated correctly
2. **JavaScript errors**: Ensure all JS dependencies are copied
3. **CSS styling issues**: Verify all CSS files are included
4. **Language not loading**: Check locale file paths

### Known Issues Fixed:
- **galBackground.png**: This file was missing from the original source, replaced with scaleBG.png as fallback
- **excanvas.js**: Not available in source, commented out (only needed for very old IE versions)
- **polyfiller.js**: Not available in source, added fallback handling for range input polyfills

### Debug Tips:
- Use browser Developer Tools (F12) to check for 404 errors
- Verify file paths are case-sensitive on Linux/Mac systems
- Ensure all files are copied with correct permissions
- Check browser console for any JavaScript errors

## License and Credits

This simulator is part of the Amrita Virtual Labs project:
- **Developed by**: Amrita University
- **Funded by**: Ministry of Electronics and Information Technology, Government of India
- **Original Source**: https://amrita.olabs.edu.in/

## Contact

For issues related to the extraction process or simulator functionality, please refer to the original Amrita Virtual Labs documentation or contact the development team.
