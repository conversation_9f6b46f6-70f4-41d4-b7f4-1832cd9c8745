$.i18n.kn = {};
 jQuery.i18n.kn.strings ={
	"Ohm's Law": "ಓಮ್ ನ ನಿಯಮ",
	"Readings": "ಮಾಪನಾಂಕಗಳು",
	"Voltage (V) :": "ವೋಲ್ಟೇಜ್ (V)",
	"Ammeter reading (I) :": "ಅಮ್ಮೀಟರ್ ನ ಮಾಪನಾಂಕಗಳು ( I)",
	"Rheostat resistance :": "ರಿಯೋಸ್ಟಾಟ್ ನ ರೋಧ",
	"Length of the resistance wire :": "ರೋಧ ತಂತಿಯ   ಉದ್ದ",
	"Show Circuit Diagram": "ಮಂಡಲ ಚಿತ್ರವನ್ನು ತೋರಿಸಿ",
	"Results": "ಫಲಿತಾಂಶಗಳು",
	"Enter the resistance of the wire:": "ತಂತಿಯ ರೋಧವನ್ನು ದಾಖಲಿಸಿ",
	"Enter the resistance per cm of the wire:": "ರೋಧ/ಸೆಂ.ಮೀ ಅನ್ನು ದಾಖಲಿಸಿ",
	"Please enter the value": "ಮೌಲ್ಯಗಳನ್ನು ಭರ್ತಿ ಮಾಡಿ",
	" V": "V",
	" A": "A",
	" Show result": "ಫಲಿತಾಂಶಗಳನ್ನು ತೋರಿಸಿ",
	"Drag and connect the terminal</br>of each apparatus as shown</br>in the circuit diagram": "ಪ್ರತಿ ಉಪಕರಣದ  ಅಗ್ರವನ್ನು ಎಳೆದು l</br>ಮಂಡಲ ಚಿತ್ರದಲ್ಲಿರುವಂತೆ </br>ಜೋಡಿಸಿ",
	"Drag the resistance wire</br>to the voltmeter": "ರೋಧ ತಂತಿಯನ್ನು </br>ವೋಲ್ಟ್ ಮೀಟರ್ ವರೆಗೆ ಎಳೆಯಿರಿ",
	"Drag the switch key</br>to the switch": "ಸ್ವಿಚ್  ಕೀ ಯನ್ನ  </br>  ಸ್ವಿಚ್ ನ ವರೆಗೆ ಎಳೆಯಿರಿ",
	"Drag the rheostat key to</br>adjust the rheostat resistance": "ರಿಯೋಸ್ಟಾಟ್ ಕೀಯನ್ನು ಎಳೆಯಿರಿ</br>ರೋಯೋಸ್ಟಾಟ್ ನ ರೋಧವನ್ನು ಸರಿ ಹೊಂದಿಸಿ",
	"Select the length of</br>the resistance wire": "ರೋಧ ತಂತಿಯ </br> ಉದ್ದವನ್ನು ಆಯ್ಕೆ ಮಾಡಿ ",
	"HELP": "ಸಹಾಯ",
	"Reset": "ಪುನಃ ಹೊಂದಿಸಿ",
	"Check": "ಪರೀಕ್ಷಿಸಿ",
	"0.5m": "0.5m",
	"0 V": "0 V",
	"0 A": ""
}